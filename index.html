<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>EquineFi - The Home of Equine Finance</title>
		<link rel="stylesheet" href="styles.css" />
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
			rel="stylesheet"
		/>
	</head>
	<body>
		<div class="container">
			<!-- Header/Navigation -->
			<header class="header">
				<div class="nav-container">
					<div class="logo">
						<h2>Logo</h2>
					</div>
					<nav class="navigation">
						<ul class="nav-menu">
							<li><a href="#about">About</a></li>
							<li><a href="#horse">$HORSE</a></li>
							<li><a href="#marketplace">Marketplace</a></li>
							<li><a href="#partners">Partners</a></li>
							<li><a href="#contacts">Contacts</a></li>
						</ul>
					</nav>
					<div class="header-cta">
						<button class="btn btn-primary">Join Presale</button>
					</div>
				</div>
			</header>

			<!-- Hero Section -->
			<main class="hero">
				<div class="hero-content">
					<div class="hero-text">
						<h1 class="hero-title">
							The Home of
							<br />
							<span class="highlight">EquineFi</span>
						</h1>
						<p class="hero-description">
							Own racehorses via NFTs, trade on the marketplace, earn rewards, stake $HORSE, bet on-chain
							and access real-time equine data — powered by Web3 and Secretariat's legacy.
						</p>
						<div class="hero-buttons">
							<button class="btn btn-primary">Join Presale</button>
							<button class="btn btn-secondary">Learn More</button>
						</div>
					</div>
					<div class="hero-image">
						<div class="horse-geometric">
							<!-- Geometric horse will be created with CSS -->
							<div class="horse-container">
								<svg viewBox="0 0 500 500" class="horse-svg">
									<defs>
										<linearGradient id="redGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
											<stop offset="0%" style="stop-color: #ff6666; stop-opacity: 0.9" />
											<stop offset="50%" style="stop-color: #ff4444; stop-opacity: 0.8" />
											<stop offset="100%" style="stop-color: #cc3333; stop-opacity: 0.7" />
										</linearGradient>
										<linearGradient id="redGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
											<stop offset="0%" style="stop-color: #ff4444; stop-opacity: 0.8" />
											<stop offset="50%" style="stop-color: #cc3333; stop-opacity: 0.7" />
											<stop offset="100%" style="stop-color: #aa2222; stop-opacity: 0.6" />
										</linearGradient>
										<linearGradient id="redGradient3" x1="50%" y1="0%" x2="50%" y2="100%">
											<stop offset="0%" style="stop-color: #ff5555; stop-opacity: 0.9" />
											<stop offset="100%" style="stop-color: #bb2222; stop-opacity: 0.6" />
										</linearGradient>
									</defs>

									<!-- Main horse head - larger geometric shape -->
									<polygon
										points="250,80 320,120 360,180 350,240 320,300 280,340 250,360 220,340 180,300 150,240 140,180 180,120"
										fill="url(#redGradient1)"
										stroke="rgba(255,68,68,0.3)"
										stroke-width="2"
									/>

									<!-- Horse muzzle -->
									<polygon
										points="250,360 280,400 250,430 220,400"
										fill="url(#redGradient2)"
										stroke="rgba(255,68,68,0.2)"
										stroke-width="1"
									/>

									<!-- Left ear -->
									<polygon
										points="200,100 210,60 230,80 220,110"
										fill="url(#redGradient3)"
										stroke="rgba(255,68,68,0.2)"
										stroke-width="1"
									/>

									<!-- Right ear -->
									<polygon
										points="280,110 270,80 290,60 300,100"
										fill="url(#redGradient3)"
										stroke="rgba(255,68,68,0.2)"
										stroke-width="1"
									/>

									<!-- Geometric mane elements -->
									<polygon
										points="150,140 120,120 100,160 130,180"
										fill="url(#redGradient2)"
										opacity="0.7"
									/>
									<polygon
										points="130,180 100,200 120,240 150,220"
										fill="url(#redGradient1)"
										opacity="0.6"
									/>
									<polygon
										points="150,220 120,260 140,300 170,280"
										fill="url(#redGradient3)"
										opacity="0.5"
									/>

									<!-- Right side mane -->
									<polygon
										points="350,140 380,120 400,160 370,180"
										fill="url(#redGradient2)"
										opacity="0.7"
									/>
									<polygon
										points="370,180 400,200 380,240 350,220"
										fill="url(#redGradient1)"
										opacity="0.6"
									/>
									<polygon
										points="350,220 380,260 360,300 330,280"
										fill="url(#redGradient3)"
										opacity="0.5"
									/>

									<!-- Additional geometric fragments -->
									<polygon
										points="80,100 110,90 120,120 90,130"
										fill="url(#redGradient1)"
										opacity="0.4"
									/>
									<polygon
										points="420,100 410,90 400,120 430,130"
										fill="url(#redGradient1)"
										opacity="0.4"
									/>
									<polygon
										points="70,200 100,190 110,220 80,230"
										fill="url(#redGradient2)"
										opacity="0.3"
									/>
									<polygon
										points="430,200 420,190 410,220 440,230"
										fill="url(#redGradient2)"
										opacity="0.3"
									/>

									<!-- Eye area highlight -->
									<circle cx="220" cy="200" r="8" fill="#ff8888" opacity="0.8" />
									<circle cx="280" cy="200" r="8" fill="#ff8888" opacity="0.8" />
								</svg>
							</div>
						</div>
					</div>
				</div>
			</main>
		</div>
	</body>
</html>
