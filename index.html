<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>EquineFi - The Home of Equine Finance</title>
		<link rel="stylesheet" href="styles.css" />
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
			rel="stylesheet"
		/>
	</head>
	<body>
		<div class="container">
			<!-- Header/Navigation -->
			<header class="header">
				<div class="nav-container">
					<div class="logo">
						<h2>Logo</h2>
					</div>
					<nav class="navigation">
						<ul class="nav-menu">
							<li><a href="#about">About</a></li>
							<li><a href="#horse">$HORSE</a></li>
							<li><a href="#marketplace">Marketplace</a></li>
							<li><a href="#partners">Partners</a></li>
							<li><a href="#contacts">Contacts</a></li>
						</ul>
					</nav>
					<div class="header-cta">
						<button class="btn btn-primary">Join Presale</button>
					</div>
				</div>
			</header>

			<!-- Hero Section -->
			<main class="hero">
				<div class="hero-content">
					<div class="hero-text">
						<h1 class="hero-title">
							The Home of
							<br />
							<span class="highlight">EquineFi</span>
						</h1>
						<p class="hero-description">
							Own racehorses via NFTs, trade on the marketplace, earn rewards, stake $HORSE, bet on-chain
							and access real-time equine data — powered by Web3 and Secretariat's legacy.
						</p>
						<div class="hero-buttons">
							<button class="btn btn-primary">Join Presale</button>
							<button class="btn btn-secondary">Learn More</button>
						</div>
					</div>
				</div>
			</main>
		</div>
	</body>
</html>
