/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "Inter", sans-serif;
	background: #1a1a1a;
	color: white;
	overflow-x: hidden;
}

.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #2a2a2a 100%);
	position: relative;
}

/* Header Styles */
.header {
	position: relative;
	z-index: 100;
	padding: 20px 0;
}

.nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.logo h2 {
	font-size: 24px;
	font-weight: 700;
	color: white;
}

.navigation {
	flex: 1;
	display: flex;
	justify-content: center;
}

.nav-menu {
	display: flex;
	list-style: none;
	gap: 40px;
}

.nav-menu a {
	color: white;
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	transition: color 0.3s ease;
}

.nav-menu a:hover {
	color: #ff4444;
}

/* Button Styles */
.btn {
	padding: 12px 24px;
	border: none;
	border-radius: 8px;
	font-weight: 600;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	display: inline-block;
}

.btn-primary {
	background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
	color: white;
}

.btn-primary:hover {
	background: linear-gradient(135deg, #ff5555 0%, #dd4444 100%);
	transform: translateY(-2px);
}

.btn-secondary {
	background: transparent;
	color: white;
	border: 2px solid #ff4444;
}

.btn-secondary:hover {
	background: #ff4444;
	color: white;
}

/* Hero Section */
.hero {
	padding: 80px 0;
	position: relative;
	overflow: hidden;
}

.hero-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 40px;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
	min-height: 70vh;
}

.hero-text {
	z-index: 10;
}

.hero-title {
	font-size: 64px;
	font-weight: 700;
	line-height: 1.1;
	margin-bottom: 24px;
}

.highlight {
	background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.hero-description {
	font-size: 18px;
	line-height: 1.6;
	color: #cccccc;
	margin-bottom: 40px;
	max-width: 500px;
}

.hero-buttons {
	display: flex;
	gap: 20px;
}

/* Horse Geometric Design */
.hero-image {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.horse-container {
	width: 500px;
	height: 500px;
	position: relative;
	animation: float 6s ease-in-out infinite;
}

.horse-svg {
	width: 100%;
	height: 100%;
	filter: drop-shadow(0 0 40px rgba(255, 68, 68, 0.4));
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10px);
	}
}

/* Additional geometric elements */
.hero-image::before {
	content: "";
	position: absolute;
	width: 200px;
	height: 200px;
	background: linear-gradient(45deg, rgba(255, 68, 68, 0.1), rgba(204, 51, 51, 0.1));
	border-radius: 50%;
	top: 20%;
	right: 10%;
	z-index: -1;
	animation: pulse 4s ease-in-out infinite;
}

.hero-image::after {
	content: "";
	position: absolute;
	width: 150px;
	height: 150px;
	background: linear-gradient(135deg, rgba(255, 68, 68, 0.08), rgba(170, 34, 34, 0.08));
	border-radius: 50%;
	bottom: 20%;
	left: 10%;
	z-index: -1;
	animation: pulse 4s ease-in-out infinite reverse;
}

@keyframes pulse {
	0%,
	100% {
		transform: scale(1);
		opacity: 0.5;
	}
	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}
}

/* Background Effects */
.hero::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 60%;
	height: 100%;
	background: radial-gradient(ellipse at center, rgba(255, 68, 68, 0.15) 0%, transparent 70%);
	z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
	.nav-container {
		padding: 0 20px;
		flex-direction: column;
		gap: 20px;
	}

	.nav-menu {
		gap: 20px;
	}

	.hero-content {
		grid-template-columns: 1fr;
		gap: 40px;
		text-align: center;
		padding: 0 20px;
	}

	.hero-title {
		font-size: 48px;
	}

	.hero-buttons {
		justify-content: center;
		flex-wrap: wrap;
	}

	.horse-container {
		width: 300px;
		height: 300px;
	}
}

@media (max-width: 480px) {
	.hero-title {
		font-size: 36px;
	}

	.hero-description {
		font-size: 16px;
	}

	.btn {
		padding: 10px 20px;
		font-size: 14px;
	}
}
